"""
测试简历渲染API的客户端脚本
"""
import requests
import json
import os
import time
import sys
from app.examples.resume_example import example_resume_data
from sample_data import resume_data

# 服务器URL
SERVER_URL = "http://localhost:18080"
PDF_SERVICE_URL = "http://localhost:3001"


def test_render_resume(template_name="templateA02.html", theme_color=None):
    """测试简历渲染API"""
    url = f"{SERVER_URL}/resume/render"
    
    # 准备查询参数
    params = {"template_name": template_name}
    if theme_color:
        params["theme_color"] = theme_color
    
    try:
        # 发送请求
        print(f"正在发送请求到简历渲染API (模板: {template_name})...")
        response = requests.post(
            url, 
            json=example_resume_data,
            params=params,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应
        if response.status_code == 200:
            # 创建输出目录（如果不存在）
            os.makedirs("output", exist_ok=True)
            
            # 保存HTML文件
            template_basename = os.path.splitext(template_name)[0]
            output_file = f"output/resume_{template_basename}.html"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write(response.text)
            print(f"成功! 简历HTML已保存到 {output_file}")
            return True
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False


def test_preview_resume(template_name="templateA02.html", theme_color=None):
    """测试简历预览API"""
    url = f"{SERVER_URL}/resume/preview"
    
    # 准备查询参数
    params = {"template_name": template_name}
    if theme_color:
        params["theme_color"] = theme_color
    
    try:
        # 发送请求
        print(f"正在发送请求到简历预览API (模板: {template_name})...")
        response = requests.post(
            url, 
            json=example_resume_data,
            params=params,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应
        if response.status_code == 200:
            # 创建输出目录（如果不存在）
            os.makedirs("output", exist_ok=True)
            
            # 保存JSON响应
            template_basename = os.path.splitext(template_name)[0]
            output_file = f"output/preview_response_{template_basename}.json"
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(response.json(), f, ensure_ascii=False, indent=2)
            
            # 保存HTML文件
            html_content = response.json().get("html", "")
            html_file = f"output/preview_{template_basename}.html"
            with open(html_file, "w", encoding="utf-8") as f:
                f.write(html_content)
                
            print(f"成功! 预览响应已保存到 {output_file}")
            print(f"HTML内容已保存到 {html_file}")
            return True
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False


def test_export_pdf(template_name="templateA02.html", theme_color=None):
    """测试简历导出PDF功能"""
    url = f"{SERVER_URL}/resume/export-pdf"
    
    # 准备查询参数
    template_basename = os.path.splitext(template_name)[0]
    params = {
        "template_name": template_name,
        "filename": f"resume_{template_basename}.pdf"
    }
    if theme_color:
        params["theme_color"] = theme_color
    
    try:
        # 发送请求
        print(f"正在发送请求到简历PDF导出API (模板: {template_name})...")
        response = requests.post(
            url, 
            json=example_resume_data,
            params=params,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应
        if response.status_code == 200:
            # 创建输出目录（如果不存在）
            os.makedirs("output", exist_ok=True)
            
            # 保存PDF文件
            output_file = f"output/resume_{template_basename}.pdf"
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"成功! 简历PDF已保存到 {output_file}")
            return True
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(response.text)
            return False
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False

def test_export_jpeg(template_name="templateA02.html", theme_color='#ffffff'):
    """测试简历导出PDF功能"""
    url = f"{SERVER_URL}/resume/export-jpeg"
    # url = f"{SERVER_URL}/resume/export-pdf"
    
    # 准备查询参数
    template_basename = os.path.splitext(template_name)[0]
    params = {
        "resume_data": resume_data,              # dict(resume_data),
        "template_id": template_basename,

        # "template_name": template_name,
        # "filename": f"resume_{template_basename}.jpeg"
    }
    if theme_color:
        params["theme_config"] = {"theme_color" : theme_color}
    # print(params)
    
    try:
        # 发送请求
        print(f"正在发送请求到简历PDF导出API (模板: {template_name})...")
        response = requests.post(
            url, 
            json=params,
            # params=params,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应
        if response.status_code == 200:
            # 创建输出目录（如果不存在）
            os.makedirs("output", exist_ok=True)
            
            # 保存PDF文件
            output_file = f"output/resume_{template_basename}.jpeg"
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"成功! 简历 jpeg 已保存到 {output_file}")
            return True
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(response.text)
            return False
        
    except requests.exceptions.HTTPError as e:
        error_info = e.response.json()
        print(error_info)
        
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False



def test_pdf_service_health():
    """测试PDF服务的健康状态"""
    # 直接测试PDF服务
    pdf_service_url = f"{PDF_SERVICE_URL}/health"
    
    try:
        print("正在检查PDF服务健康状态...")
        response = requests.get(pdf_service_url, timeout=3)
        
        if response.status_code == 200:
            print("PDF服务运行正常!")
            print(f"响应: {json.dumps(response.json(), indent=2)}")
            return True
        else:
            print(f"PDF服务返回错误: {response.status_code}")
            print(response.text)
            return False
    except Exception as e:
        print(f"无法连接到PDF服务: {str(e)}")
        print("请确保PDF服务已启动并运行在 http://localhost:3001")
        return False

def test_node_jpeg_():
    url = 'http://127.0.0.1:3000/convertjpeg'
    
    with open('resume.html') as f:
        html_content = f.read()
    print(html_content[:50])

    body = {
        "html": html_content,
        "options": {
            "imageOptions": {
                "type": "jpeg",
                "quality": 90,
                "fullPage": True
            }
        }
    }

    try:
        # 发送请求
        # print(f"正在发送请求到简历PDF导出API (模板: {template_name})...")
        response = requests.post(
            url, 
            json=body,
            # params=params,
            headers={"Content-Type": "application/json"}
        )
        
        # 检查响应
        if response.status_code == 200:
            # 创建输出目录（如果不存在）
            os.makedirs("test_results", exist_ok=True)
            
            # 保存PDF文件
            output_file = f"test_results/resume.jpeg"
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"成功! 简历 jpeg 已保存到 {output_file}")
            return True
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(response.text)
            return False
        
    except requests.exceptions.HTTPError as e:
        error_info = e.response.json()
        print(error_info)
        
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return False


def list_templates():
    """获取可用的模板列表"""
    url = f"{SERVER_URL}/resume/templates"
    try:
        print("获取可用模板列表...")
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            templates = data.get("templates", [])
            print(f"可用模板: {', '.join(templates)}")
            return templates
        else:
            print(f"错误: HTTP状态码 {response.status_code}")
            print(response.text)
            return []
    except Exception as e:
        print(f"请求失败: {str(e)}")
        return []


if __name__ == "__main__":
    print("=== 测试简历API ===")
    
    # 先测试PDF服务健康状态
    # pdf_service_ok = test_pdf_service_health()
    # if not pdf_service_ok:
    #     print("\n警告: PDF服务可能不可用，PDF导出功能可能无法正常工作")
    #     print("请确保PDF转换服务已启动: 'cd pdf-service && npm start'\n")
    
    # 获取可用模板
    # templates = list_templates()[1:]
    
    # 没有可用模板时或者有问题时，使用默认模板
    # if not templates:
    #     templates = ["templateA02.html"]


    template = 'templateA04'
    for i in range(1, 2):
        start = time.time()
        test_export_jpeg(template_name=template, theme_color='#ffffff')
        end = time.time()
        print(f"导出耗时: {end - start:.4f}秒")
    # print(f"可用模板: {', '.join(templates)}")

    # 测试每个模板
    # for template in templates:
    # print(f"\n测试模板: {template}")
    
    # # 测试渲染简历
    # print(f"\n1. 测试渲染简历API (模板: {template}):")
    # test_render_resume(template)
    
    # # 测试预览简历
    # print(f"\n2. 测试预览简历API (模板: {template}):")
    # test_preview_resume(template)
    
    # # 如果PDF服务可用，测试导出PDF
    # if pdf_service_ok:
    #     print(f"\n3. 测试导出PDF API (模板: {template}):")
    #     test_export_pdf(template)
    # else:
    #     print(f"\n3. 跳过测试导出PDF，因为PDF服务不可用")
    
    print("\n测试完成!") 