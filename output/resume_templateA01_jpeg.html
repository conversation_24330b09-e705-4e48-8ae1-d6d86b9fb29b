<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>李华的简历</title>
  <link rel="stylesheet" href="http://localhost:18080/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: #44546b;
      --theme-color-rgb: 43, 108, 176;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #333333;
      --secondary-color: #666666;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      padding: 20px 40px 40px 40px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      position: relative;
    }

    /* 左侧装饰线 */
    .resume-container::before {
      content: '';
      position: absolute;
      left: 60px;
      top: 100px;
      bottom: 40px;
      width: 1px;
      background-color: var(--theme-color);
      transform: scaleX(0.3);
      transform-origin: left;
    }

    /* 头部区域 */
    .resume-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: -10px;
      margin-bottom: 20px;
    }

    .resume-title {
      text-align: left;
      font-size: calc(var(--base-font-size) * 2.5);
      font-weight: bold;
      color: var(--theme-color);
    }

    .icon-group {
      display: flex;
      align-items: center;
      gap: 18px;
    }

    .icon-circle {
      width: 18px;
      height: 18px;
      padding: 10px;
      border-radius: 50%;
      background-color: var(--theme-color);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon-circle i {
      color: white;
      font-size: 14px;
    }

    /* 提醒图标 */
    .tixing-wrapper {
      position: relative;
      margin: 20px 0;
      height: 40px;
      width: 100%;
      display: flex;
      align-items: center;
    }

    .tixing-icon {
      width: 120%;
      height: 25px;
      background-color: var(--theme-color);
      position: absolute;
      left: -140px;
      top: -20px;
      z-index: 2;
      -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
    }

    /* 添加第二个提醒图标 */
    .tixing-wrapper::after {
      content: '';
      width: 140%;
      height: 30px;
      position: absolute;
      left: -50px;
      top: -16px;
      z-index: 1;
      transform: scale(1);
      transform-origin: left center;
      background-color: rgba(var(--theme-color-rgb, 43, 108, 176), 0.3);
      -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
    }

    /* 基本信息区域 */
    .basic-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: -30px;
      padding: 0 20px;
    }

    .avatar {
      width: 150px;
      height: 200px;
      object-fit: cover;
    }

    .info-content {
      flex: 1.5;
      margin-right: 20px;
      width: auto;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      width: 100%;
    }

    .info-item {
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 各部分通用样式 */
    .section {
      margin-top: -60px;
      margin-bottom: 50px;
      margin-left: 20px;
      margin-right: 20px;
    }

    .title-bg {
      position: absolute;
      left: -34rpx;
      top: 50%;
      width: 153%;
      height: 280rpx;
      z-index: 1;
      transform: translateY(-50%) scale(1.2);
      transform-origin: left center;
      background-color: var(--theme-color, #2B6CB0);
      -webkit-mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
    }
    
    .title {
      position: relative;
      z-index: 2;
      font-size: calc(var(--font-size, 12rpx) + 4rpx);
      line-height: calc(var(--spacing, 1) * 1.3);
      color: #FFFFFF;
      font-weight: bold;
      padding: 20rpx 0;
    }

    /* 确保标题不会被截断 */
    .title:empty + .title-bg {
      display: none;
    }

    .content {
      margin-bottom: 15px;
      margin-top: -30px;
      padding: 0 20px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      gap: 10px;
      display: flex;
      flex-wrap: wrap;
    }

    /* 求职意向样式 */
    .job-intention-content {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      line-height: calc(var(--spacing) * 1.3);
    }

    .job-intention-item {
      width: calc(50% - 10px);
      box-sizing: border-box;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
    }

    /* 教育经历样式 */
    .education-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      min-height: 1.5em;
    }

    .school {
      font-weight: bold;
      width: 35%;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .major-degree {
      font-weight: bold;
      width: 35%;
      text-align: center;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .edu-date {
      width: 30%;
      text-align: right;
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: bold;
    }

    .edu-description {
      color: var(--text-color);
      margin-top: 5px;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: var(--base-font-size);
      width: 100%;
    }

    .courses-label {
      font-weight: bold;
    }

    /* 工作经历和实习经历样式 */
    .internship-header {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .company {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .company:first-child {
      flex: 0 0 35%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .company:nth-child(2) {
      flex: 0 0 35%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .time {
      flex: 0 0 30%;
      text-align: right;
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: bold;
    }

    .description {
      color: var(--text-color);
      margin-top: 5px;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      width: 100%;
    }

    /* 在校经历样式 */
    .school-experience-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 5px;
    }

    .activity {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      flex: 1;
    }

    .time {
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      text-align: right;
      font-weight: bold;
    }

    /* 技能特长、获奖证书、兴趣爱好样式 */
    .skill, .award, .interest {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
    }

    /* 自我评价样式 */
    .evaluation-content {
      max-width: 100%;
      column-count: 1;
      column-gap: 20px;
    }

    .evaluation-content p {
      margin-bottom: 8px;
      break-inside: avoid;
      page-break-inside: avoid;
    }

    @media (min-width: 768px) {
      .evaluation-content {
        column-count: 2;
      }
    }

    /* 自定义模块样式 */
    .custom-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 5px;
    }

    .custom-name {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      flex: 1;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        width: 210mm;
        height: 297mm;
        padding: 10mm;
        margin: 0;
        box-shadow: none;
      }

      .section, .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-title">个人简历</h1>
      <div class="icon-group">
        <div class="icon-circle"><i class="fas fa-pencil-alt"></i></div>
        <div class="icon-circle"><i class="fas fa-graduation-cap"></i></div>
        <div class="icon-circle"><i class="fas fa-briefcase"></i></div>
      </div>
    </header>

    <!-- 提醒图标 -->
    <div class="tixing-wrapper">
      <div class="tixing-icon"></div>
    </div>

    <!-- 基本信息 -->
    
    <section class="section">
      <div class="title-wrapper">
        <div class="title-bg"></div>
        <div class="title">基本信息</div>
      </div>
      <div class="basic-info">
        <div class="info-content">
          <div class="info-grid">
            <div class="info-item">姓　　名：李华</div>
            <div class="info-item">生　　日：1995-05-20</div>
            <div class="info-item">电　　话：13912345678</div>
            <div class="info-item">婚　　姻：未婚</div>
            <div class="info-item">邮　　箱：<EMAIL></div>
            <div class="info-item">政治面貌：群众</div>
            <div class="info-item">城　　市：上海</div>
            <div class="info-item">民　　族：汉族</div>
            <div class="info-item">年　　龄：28岁</div>
            <div class="info-item">籍　　贯：浙江杭州</div>
            <div class="info-item">性　　别：女</div>
            <div class="info-item">身　　高：165cmcm</div>
            <div class="info-item">微　　信：LiHua_2023</div>
            <div class="info-item">体　　重：52kgkg</div>
            <div class="info-item">学　　历：硕士</div>
          </div>
        </div>
        
        <img class="avatar" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=" alt="个人照片" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22200%22%20viewBox%3D%220%200%20150%20200%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22150%22%20height%3D%22200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'">
        
      </div>
    </section>
    

    <!-- 根据moduleOrders排序显示各个模块 -->
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">教育背景</div>
        </div>
        
        <div class="content">
          <div class="education-header">
            <div class="school">复旦大学</div>
            <div class="major-degree">软件工程/硕士</div>
            <div class="edu-date">2018-09 - 2021-06</div>
          </div>
          <div class="edu-description">
            <span class="courses-label">主修课程：</span>研究方向：前端框架优化，发表2篇核心期刊论文
          </div>
        </div>
        
        <div class="content">
          <div class="education-header">
            <div class="school">浙江大学</div>
            <div class="major-degree">计算机科学与技术/学士</div>
            <div class="edu-date">2014-09 - 2018-06</div>
          </div>
          <div class="edu-description">
            <span class="courses-label">主修课程：</span>GPA 3.8/4.0，ACM校队成员
          </div>
        </div>
        
      </section>
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">在校经历</div>
        </div>
        
        <div class="content">
          <div class="school-experience-header">
            <div class="activity">学生会主席</div>
            <div class="time">2016-09 - 2017-06</div>
          </div>
          
          <div class="description">组织校级技术竞赛，参与人数超过500人</div>
          
        </div>
        
      </section>
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">实习经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">腾讯科技</div>
            <div class="company">前端开发实习生</div>
            <div class="time">2020-07 - 2020-12</div>
          </div>
          
          <div class="description">参与微信小程序性能优化项目，首屏加载速度提升40%</div>
          
        </div>
        
      </section>
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">工作经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">阿里巴巴集团</div>
            <div class="company">前端开发工程师</div>
            <div class="time">2021-07 - 至今</div>
          </div>
          
          <div class="description">负责电商中台前端架构设计，主导Vue3+TypeScript技术栈落地</div>
          
        </div>
        
      </section>
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">项目经历</div>
        </div>
        
        <div class="content">
          <div class="internship-header">
            <div class="company">跨平台电商系统</div>
            <div class="company">技术负责人</div>
            <div class="time">2022-03 - 2023-01</div>
          </div>
          
          <div class="description">基于Flutter+Node.js的跨平台解决方案，日活用户突破100万</div>
          
        </div>
        
      </section>
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">技能证书</div>
        </div>
        <div class="content">
          
          <div class="skill">React/Vue</div>
          
          <div class="skill">TypeScript</div>
          
          <div class="skill">Webpack</div>
          
          <div class="skill">Node.js</div>
          
          <div class="skill">Python</div>
          
          <div class="skill">Docker</div>
          
          <div class="skill">Jenkins</div>
          
          <div class="skill">GitLab CI/CD</div>
          
        </div>
      </section>
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">奖项荣誉</div>
        </div>
        <div class="content">
          
          <div class="award">2022年阿里技术卓越奖</div>
          
          <div class="award">2021年QCon优秀演讲者</div>
          
          <div class="award">2020年全国大学生计算机设计大赛一等奖</div>
          
        </div>
      </section>
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">兴趣爱好</div>
        </div>
        <div class="content">
          
          <div class="interest">开源社区</div>
          
          <div class="interest">AI技术研究</div>
          
          <div class="interest">马拉松</div>
          
          <div class="interest">古典音乐</div>
          
        </div>
      </section>
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自我评价</div>
        </div>
        <div class="content">
          <div class="description evaluation-content">
            
              
                
                  <p>技术视野广阔，具备复杂系统架构能力, 团队管理经验丰富，培养多名中级工程师</p>
                
              
            
          </div>
        </div>
      </section>
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块1</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">专利信息</div>
            <div class="time">2021-05 - 2021-05</div>
          </div>
          
          <div class="description">一种前端资源懒加载方法（专利号：ZL2021XXXXXXX）</div>
          
        </div>
        
      </section>
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块2</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">职业认证</div>
            <div class="time">2022-10 - 2025-10</div>
          </div>
          
          <div class="description">AWS Certified Solutions Architect</div>
          
        </div>
        
      </section>
      

      <!-- 自定义模块3 -->
      
    
      <!-- 求职意向 -->
      

      <!-- 教育背景 -->
      

      <!-- 实习经历 -->
      

      <!-- 工作经历 -->
      

      <!-- 项目经历 -->
      

      <!-- 在校经历 -->
      

      <!-- 技能证书 -->
      

      <!-- 奖项荣誉 -->
      

      <!-- 兴趣爱好 -->
      

      <!-- 自我评价 -->
      

      <!-- 自定义模块1 -->
      

      <!-- 自定义模块2 -->
      

      <!-- 自定义模块3 -->
      
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自定义模块3</div>
        </div>
        
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">技术培训</div>
            <div class="time">2023-01 - 2023-03</div>
          </div>
          
          <div class="description">Vue3高级特性培训（参与人数：120人）</div>
          
        </div>
        
      </section>
      
    
  </div>
</body>
</html>