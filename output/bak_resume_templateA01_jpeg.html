<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>李华的简历</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    :root {
      --theme-color: #44546b;
      --base-font-size: 11pt;
      --max-font-size: 12pt;
      --spacing: 1.2;
      --text-color: #333333;
      --secondary-text-color: #555555;
      --border-color: #e0e0e0;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      overflow: hidden;
      font-size: var(--base-font-size);
      line-height: var(--spacing);
      color: var(--text-color);
      position: relative;
    }

    /* 左侧装饰条 */
    .side-decoration {
      position: absolute;
      left: 0;
      top: 0;
      width: 15px;
      height: 100%;
      background-color: var(--theme-color);
    }

    /* 头部区域 */
    .resume-header {
      padding: 30px 40px 20px 40px;
      border-bottom: 2px solid var(--theme-color);
      margin-left: 15px;
    }

    .resume-header-name {
      font-size: calc(var(--base-font-size) * 2.2);
      font-weight: bold;
      margin-bottom: 15px;
      color: var(--theme-color);
    }

    .resume-header-info {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 10px;
      font-size: var(--base-font-size);
    }

    .resume-header-info p {
      display: flex;
      align-items: center;
    }

    .resume-header-info i {
      margin-right: 8px;
      color: var(--theme-color);
      width: 16px;
      text-align: center;
    }

    /* 主体内容区域 */
    .resume-content {
      padding: 0 40px 30px 40px;
      margin-left: 15px;
    }

    /* 各部分通用样式 */
    .section {
      margin-top: 20px;
    }

    .section-header {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      position: relative;
    }

    .section-title {
      font-size: calc(var(--base-font-size) * 1.3);
      font-weight: bold;
      color: var(--theme-color);
      padding-right: 15px;
      display: flex;
      align-items: center;
    }

    .section-title i {
      margin-right: 8px;
    }

    .section-line {
      flex-grow: 1;
      height: 1px;
      background-color: var(--theme-color);
    }

    /* 教育经历样式 */
    .education-item {
      margin-bottom: 15px;
    }

    .education-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .education-school {
      color: var(--theme-color);
    }

    .education-date {
      color: var(--secondary-text-color);
    }

    .education-details {
      margin-left: 20px;
    }

    /* 工作经历样式 */
    .experience-item {
      margin-bottom: 15px;
    }

    .experience-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .experience-company {
      color: var(--theme-color);
    }

    .experience-date {
      color: var(--secondary-text-color);
    }

    .experience-position {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .experience-description {
      margin-left: 20px;
    }

    /* 项目经历样式 */
    .project-item {
      margin-bottom: 15px;
    }

    .project-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .project-name {
      color: var(--theme-color);
    }

    .project-date {
      color: var(--secondary-text-color);
    }

    .project-role {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .project-description {
      margin-left: 20px;
    }

    /* 实习经历样式 */
    .internship-item {
      margin-bottom: 15px;
    }

    .internship-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .internship-company {
      color: var(--theme-color);
    }

    .internship-date {
      color: var(--secondary-text-color);
    }

    .internship-position {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .internship-description {
      margin-left: 20px;
    }

    /* 在校经历样式 */
    .school-experience-item {
      margin-bottom: 10px;
      padding-left: 20px;
    }

    .school-experience-date {
      font-weight: bold;
      color: var(--theme-color);
    }

    /* 技能列表样式 */
    .skills-list {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding-left: 20px;
    }

    .skill-item {
      background-color: #f5f5f5;
      padding: 5px 12px;
      border-radius: 4px;
      border-left: 3px solid var(--theme-color);
    }

    /* 奖项荣誉样式 */
    .awards-list {
      padding-left: 20px;
    }

    .award-item {
      margin-bottom: 8px;
      position: relative;
      padding-left: 15px;
    }

    .award-item:before {
      content: "•";
      position: absolute;
      left: 0;
      color: var(--theme-color);
      font-weight: bold;
    }

    /* 兴趣爱好样式 */
    .interests-list {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      padding-left: 20px;
    }

    .interest-item {
      background-color: #f5f5f5;
      padding: 5px 12px;
      border-radius: 4px;
      border-left: 3px solid var(--theme-color);
    }

    /* 自我评价样式 */
    .evaluation-content {
      padding-left: 20px;
    }

    /* 自定义模块样式 */
    .custom-item {
      margin-bottom: 15px;
    }

    .custom-header {
      display: flex;
      justify-content: space-between;
      font-weight: bold;
      margin-bottom: 5px;
    }

    .custom-name {
      color: var(--theme-color);
    }

    .custom-date {
      color: var(--secondary-text-color);
    }

    .custom-role {
      font-weight: bold;
      margin-bottom: 5px;
      margin-left: 20px;
    }

    .custom-content {
      margin-left: 20px;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        box-shadow: none;
        width: 100%;
        min-height: 0;
      }

      .section, .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }

      .experience-item, .project-item, .education-item,
      .internship-item, .school-experience-item, .custom-item {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <!-- 左侧装饰条 -->
    <div class="side-decoration"></div>

    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-header-name">李华</h1>
      <div class="resume-header-info">
        <p><i class="fas fa-user"></i>女</p>
        <p><i class="fas fa-birthday-cake"></i>28岁</p>
        <p><i class="fas fa-graduation-cap"></i>硕士</p>
        <p><i class="fas fa-phone"></i>13912345678</p>
        <p><i class="fas fa-envelope"></i><EMAIL></p>
        <p><i class="fas fa-map-marker-alt"></i>上海</p>
        <p><i class="fab fa-weixin"></i>LiHua_2023</p>
        <p><i class="fas fa-bullseye"></i>求职意向: 高级前端开发工程师</p>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <main class="resume-content">
      <!-- 求职意向 -->
      
      <section class="section" id="job-intention-section">
        <div class="section-header">
          <h2 class="section-title"><i class="fas fa-bullseye"></i>求职意向</h2>
          <div class="section-line"></div>
        </div>
        <div class="section-content" style="padding-left: 20px;">
          <p><strong>期望城市：</strong>上海</p>
          <p><strong>期望薪资：</strong>25k-35k</p>
          <p><strong>求职状态：</strong>在职，寻求更好机会</p>
        </div>
      </section>
      

      <!-- 根据moduleOrders排序显示各个模块 -->
      
        
        <section class="section" id="education-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-graduation-cap"></i>教育背景</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="education-item">
              <div class="education-header">
                <span class="education-school">复旦大学</span>
                <span class="education-date">2018-09 - 2021-06</span>
              </div>
              <div class="education-details">
                <p>软件工程 (硕士)</p>
                <p>研究方向：前端框架优化，发表2篇核心期刊论文</p>
              </div>
            </div>
            
            <div class="education-item">
              <div class="education-header">
                <span class="education-school">浙江大学</span>
                <span class="education-date">2014-09 - 2018-06</span>
              </div>
              <div class="education-details">
                <p>计算机科学与技术 (学士)</p>
                <p>GPA 3.8/4.0，ACM校队成员</p>
              </div>
            </div>
            
          </div>
        </section>
        

        

        

        

        

        

        

        

        

        

        

        
      
        

        

        

        

        
        <section class="section" id="school-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-school"></i>在校经历</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="school-experience-item">
              <span class="school-experience-date">2016-09 - 2017-06 (学生会主席):</span>
              <span>组织校级技术竞赛，参与人数超过500人</span>
            </div>
            
          </div>
        </section>
        

        

        

        

        

        

        

        
      
        

        

        
        <section class="section" id="internship-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-id-badge"></i>实习经历</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="internship-item">
              <div class="internship-header">
                <span class="internship-company">腾讯科技</span>
                <span class="internship-date">2020-07 - 2020-12</span>
              </div>
              <div class="internship-position">前端开发实习生</div>
              
              <div class="internship-description">参与微信小程序性能优化项目，首屏加载速度提升40%</div>
              
            </div>
            
          </div>
        </section>
        

        

        

        

        

        

        

        

        

        
      
        

        
        <section class="section" id="work-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-briefcase"></i>工作经历</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="experience-item">
              <div class="experience-header">
                <span class="experience-company">阿里巴巴集团</span>
                <span class="experience-date">2021-07 - 至今</span>
              </div>
              <div class="experience-position">前端开发工程师</div>
              
              <div class="experience-description">负责电商中台前端架构设计，主导Vue3+TypeScript技术栈落地</div>
              
            </div>
            
          </div>
        </section>
        

        

        

        

        

        

        

        

        

        

        
      
        

        

        

        
        <section class="section" id="project-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-project-diagram"></i>项目经历</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="project-item">
              <div class="project-header">
                <span class="project-name">跨平台电商系统</span>
                <span class="project-date">2022-03 - 2023-01</span>
              </div>
              <div class="project-role">技术负责人</div>
              
              <div class="project-description">基于Flutter+Node.js的跨平台解决方案，日活用户突破100万</div>
              
            </div>
            
          </div>
        </section>
        

        

        

        

        

        

        

        

        
      
        

        

        

        

        

        
        <section class="section" id="skills-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-certificate"></i>技能证书</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="skills-list">
              
              <div class="skill-item">React/Vue</div>
              
              <div class="skill-item">TypeScript</div>
              
              <div class="skill-item">Webpack</div>
              
              <div class="skill-item">Node.js</div>
              
              <div class="skill-item">Python</div>
              
              <div class="skill-item">Docker</div>
              
              <div class="skill-item">Jenkins</div>
              
              <div class="skill-item">GitLab CI/CD</div>
              
            </div>
          </div>
        </section>
        

        

        

        

        

        

        
      
        

        

        

        

        

        

        
        <section class="section" id="awards-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-award"></i>奖项荣誉</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="awards-list">
              
              <div class="award-item">2022年阿里技术卓越奖</div>
              
              <div class="award-item">2021年QCon优秀演讲者</div>
              
              <div class="award-item">2020年全国大学生计算机设计大赛一等奖</div>
              
            </div>
          </div>
        </section>
        

        

        

        

        

        
      
        

        

        

        

        

        

        

        
        <section class="section" id="interests-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-heart"></i>兴趣爱好</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="interests-list">
              
              <div class="interest-item">开源社区</div>
              
              <div class="interest-item">AI技术研究</div>
              
              <div class="interest-item">马拉松</div>
              
              <div class="interest-item">古典音乐</div>
              
            </div>
          </div>
        </section>
        

        

        

        

        
      
        

        

        

        

        

        

        

        

        
        <section class="section" id="evaluation-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-comment"></i>自我评价</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            <div class="evaluation-content">
              
              <p>技术视野广阔，具备复杂系统架构能力, 团队管理经验丰富，培养多名中级工程师</p>
              
            </div>
          </div>
        </section>
        

        

        

        
      
        

        

        

        

        

        

        

        

        

        
        <section class="section" id="custom1-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-star"></i>自定义模块1</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">专利信息</span>
                <span class="custom-date">2021-05 - 2021-05</span>
              </div>
              
              <div class="custom-role">发明人</div>
              
              
              <div class="custom-content">一种前端资源懒加载方法（专利号：ZL2021XXXXXXX）</div>
              
            </div>
            
          </div>
        </section>
        

        

        
      
        

        

        

        

        

        

        

        

        

        

        
        <section class="section" id="custom2-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-star"></i>自定义模块2</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">职业认证</span>
                <span class="custom-date">2022-10 - 2025-10</span>
              </div>
              
              <div class="custom-role">持证者</div>
              
              
              <div class="custom-content">AWS Certified Solutions Architect</div>
              
            </div>
            
          </div>
        </section>
        

        
      
        

        

        

        

        

        

        

        

        

        

        

        
        <section class="section" id="custom3-section">
          <div class="section-header">
            <h2 class="section-title"><i class="fas fa-star"></i>自定义模块3</h2>
            <div class="section-line"></div>
          </div>
          <div class="section-content">
            
            <div class="custom-item">
              <div class="custom-header">
                <span class="custom-name">技术培训</span>
                <span class="custom-date">2023-01 - 2023-03</span>
              </div>
              
              <div class="custom-role">讲师</div>
              
              
              <div class="custom-content">Vue3高级特性培训（参与人数：120人）</div>
              
            </div>
            
          </div>
        </section>
        
      
    </main>
  </div>
</body>
</html>