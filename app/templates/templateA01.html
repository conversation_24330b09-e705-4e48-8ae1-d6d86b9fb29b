<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{ resume.basicInfo.name | default("我的") }}的简历</title>
  <link rel="stylesheet" href="{{ base_url|default('') }}/static/fontawesome/css/all.min.css">
  <style>
    :root {
      --theme-color: {{ theme_color | default('#2B6CB0') }};
      --theme-color-rgb: {{ theme_color_rgb | default('43, 108, 176') }};
      --base-font-size: {{ base_font_size | default(11) }}pt;
      --max-font-size: {{ max_font_size | default(12) }}pt;
      --spacing: {{ spacing | default(1.2) }};
      --text-color: #333333;
      --secondary-color: #666666;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    }

    body {
      background-color: #f0f0f0;
      display: flex;
      justify-content: center;
      padding: 10px 0;
      overflow-x: hidden;
    }

    .resume-container {
      width: 210mm;
      min-height: 297mm;
      background-color: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
      padding: 20px 40px 40px 40px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      position: relative;
    }

    /* 左侧装饰线 */
    .resume-container::before {
      content: '';
      position: absolute;
      left: 60px;
      top: 100px;
      bottom: 40px;
      width: 1px;
      background-color: var(--theme-color);
      transform: scaleX(0.3);
      transform-origin: left;
    }

    /* 头部区域 */
    .resume-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: -10px;
      margin-bottom: 20px;
    }

    .resume-title {
      text-align: left;
      font-size: calc(var(--base-font-size) * 2.5);
      font-weight: bold;
      color: var(--theme-color);
    }

    .icon-group {
      display: flex;
      align-items: center;
      gap: 18px;
    }

    .icon-circle {
      width: 18px;
      height: 18px;
      padding: 10px;
      border-radius: 50%;
      background-color: var(--theme-color);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .icon-circle i {
      color: white;
      font-size: 14px;
    }

    /* 提醒图标 */
    .tixing-wrapper {
      position: relative;
      margin: 20px 0;
      height: 40px;
      width: 100%;
      display: flex;
      align-items: center;
    }

    .tixing-icon {
      width: 120%;
      height: 25px;
      background-color: var(--theme-color);
      position: absolute;
      left: -140px;
      top: -20px;
      z-index: 2;
      -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
    }

    /* 添加第二个提醒图标 */
    .tixing-wrapper::after {
      content: '';
      width: 140%;
      height: 30px;
      position: absolute;
      left: -50px;
      top: -16px;
      z-index: 1;
      transform: scale(1);
      transform-origin: left center;
      background-color: rgba(var(--theme-color-rgb, 43, 108, 176), 0.3);
      -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
    }

    /* 基本信息区域 */
    .basic-info {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      margin-bottom: 20px;
      margin-top: -30px;
      padding: 0 20px;
    }

    .avatar {
      width: 150px;
      height: 200px;
      object-fit: cover;
    }

    .info-content {
      flex: 1.5;
      margin-right: 20px;
      width: auto;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 10px;
      width: 100%;
    }

    .info-item {
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    /* 各部分通用样式 */
    .section {
      margin-top: -60px;
      margin-bottom: 50px;
      margin-left: 20px;
      margin-right: 20px;
    }

    .title-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      width: 100%;
    }

    .title-bg {
      position: absolute;
      left: -34px;
      top: 50%;
      width: 300px;
      height: 280px;
      z-index: 1;
      transform: translateY(-50%) scale(1.2);
      transform-origin: left center;
      background-color: var(--theme-color);
      -webkit-mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
      mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
    }

    /* 标题后的斜线和横线 */
    .title-wrapper::after {
      content: '';
      position: absolute;
      left: 250px;
      top: 50%;
      right: 0;
      height: 1px;
      background-color: var(--theme-color);
      z-index: 0;
    }

    .title-wrapper::before {
      content: '';
      position: absolute;
      left: 200px;
      top: 0;
      width: 50px;
      height: 100%;
      background: linear-gradient(135deg, transparent 0%, transparent 49%, var(--theme-color) 50%, transparent 51%, transparent 100%);
      z-index: 0;
    }

    .title {
      position: relative;
      z-index: 2;
      font-size: calc(var(--base-font-size) + 4px);
      line-height: calc(var(--spacing) * 1.3);
      color: #FFFFFF;
      font-weight: bold;
      padding: 20px 30px;
      white-space: nowrap;
      min-width: 120px;
      max-width: 200px;
      box-sizing: border-box;
    }

    /* 确保标题不会被截断 */
    .title:empty + .title-bg {
      display: none;
    }

    .content {
      margin-bottom: 15px;
      margin-top: -30px;
      padding: 0 20px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
      gap: 10px;
      display: flex;
      flex-wrap: wrap;
    }

    /* 求职意向样式 */
    .job-intention-content {
      display: flex;
      flex-wrap: wrap;
      gap: 15px;
      line-height: calc(var(--spacing) * 1.3);
    }

    .job-intention-item {
      width: calc(50% - 10px);
      box-sizing: border-box;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
    }

    /* 教育经历样式 */
    .education-header {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 10px;
      min-height: 1.5em;
    }

    .school {
      font-weight: bold;
      width: 35%;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .major-degree {
      font-weight: bold;
      width: 35%;
      text-align: center;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .edu-date {
      width: 30%;
      text-align: right;
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: bold;
    }

    .edu-description {
      color: var(--text-color);
      margin-top: 5px;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: var(--base-font-size);
      width: 100%;
    }

    .courses-label {
      font-weight: bold;
    }

    /* 工作经历和实习经历样式 */
    .internship-header {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .company {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .company:first-child {
      flex: 0 0 35%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .company:nth-child(2) {
      flex: 0 0 35%;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .internship-header .time {
      flex: 0 0 30%;
      text-align: right;
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: bold;
    }

    .description {
      color: var(--text-color);
      margin-top: 5px;
      white-space: pre-wrap;
      word-break: break-all;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      width: 100%;
    }

    /* 在校经历样式 */
    .school-experience-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 5px;
    }

    .activity {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      flex: 1;
    }

    .time {
      color: var(--secondary-color);
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      text-align: right;
      font-weight: bold;
    }

    /* 技能特长、获奖证书、兴趣爱好样式 */
    .skill, .award, .interest {
      display: inline-block;
      margin-right: 10px;
      margin-bottom: 10px;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      color: var(--text-color);
    }

    /* 自我评价样式 */
    .evaluation-content {
      max-width: 100%;
      column-count: 1;
      column-gap: 20px;
    }

    .evaluation-content p {
      margin-bottom: 8px;
      break-inside: avoid;
      page-break-inside: avoid;
    }

    @media (min-width: 768px) {
      .evaluation-content {
        column-count: 2;
      }
    }

    /* 自定义模块样式 */
    .custom-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      margin-bottom: 5px;
    }

    .custom-name {
      font-weight: bold;
      font-size: var(--base-font-size);
      line-height: calc(var(--spacing) * 1.3);
      flex: 1;
    }

    /* 隐藏空模块 */
    .hidden {
      display: none;
    }

    /* 打印样式 */
    @page {
      size: A4;
      margin: 0;
    }

    @media print {
      body {
        background-color: white;
        padding: 0;
      }

      .resume-container {
        width: 210mm;
        height: 297mm;
        padding: 10mm;
        margin: 0;
        box-shadow: none;
      }

      .section, .section-header {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  </style>
</head>
<body>
  <div class="resume-container">
    <!-- 头部区域 -->
    <header class="resume-header">
      <h1 class="resume-title">个人简历</h1>
      <div class="icon-group">
        <div class="icon-circle"><i class="fas fa-pencil-alt"></i></div>
        <div class="icon-circle"><i class="fas fa-graduation-cap"></i></div>
        <div class="icon-circle"><i class="fas fa-briefcase"></i></div>
      </div>
    </header>

    <!-- 提醒图标 -->
    <div class="tixing-wrapper">
      <div class="tixing-icon"></div>
    </div>

    <!-- 基本信息 -->
    {% if resume.basicInfo %}
    <section class="section">
      <div class="title-wrapper">
        <div class="title-bg"></div>
        <div class="title">基本信息</div>
      </div>
      <div class="basic-info">
        <div class="info-content">
          <div class="info-grid">
            {% if resume.basicInfo.name %}<div class="info-item">姓　　名：{{ resume.basicInfo.name }}</div>{% endif %}
            {% if resume.basicInfo.birthday %}<div class="info-item">生　　日：{{ resume.basicInfo.birthday }}</div>{% endif %}
            {% if resume.basicInfo.phone %}<div class="info-item">电　　话：{{ resume.basicInfo.phone }}</div>{% endif %}
            {% if resume.basicInfo.marriage %}<div class="info-item">婚　　姻：{{ resume.basicInfo.marriage }}</div>{% endif %}
            {% if resume.basicInfo.email %}<div class="info-item">邮　　箱：{{ resume.basicInfo.email }}</div>{% endif %}
            {% if resume.basicInfo.politics %}<div class="info-item">政治面貌：{{ resume.basicInfo.politics }}</div>{% endif %}
            {% if resume.basicInfo.city %}<div class="info-item">城　　市：{{ resume.basicInfo.city }}</div>{% endif %}
            {% if resume.basicInfo.nation %}<div class="info-item">民　　族：{{ resume.basicInfo.nation }}</div>{% endif %}
            {% if resume.basicInfo.age %}<div class="info-item">年　　龄：{{ resume.basicInfo.age }}岁</div>{% endif %}
            {% if resume.basicInfo.hometown %}<div class="info-item">籍　　贯：{{ resume.basicInfo.hometown }}</div>{% endif %}
            {% if resume.basicInfo.gender %}<div class="info-item">性　　别：{{ resume.basicInfo.gender }}</div>{% endif %}
            {% if resume.basicInfo.height %}<div class="info-item">身　　高：{{ resume.basicInfo.height }}cm</div>{% endif %}
            {% if resume.basicInfo.wechat %}<div class="info-item">微　　信：{{ resume.basicInfo.wechat }}</div>{% endif %}
            {% if resume.basicInfo.weight %}<div class="info-item">体　　重：{{ resume.basicInfo.weight }}kg</div>{% endif %}
            {% if resume.basicInfo.educationLevel %}<div class="info-item">学　　历：{{ resume.basicInfo.educationLevel }}</div>{% endif %}
          </div>
        </div>
        {% if resume.basicInfo.photoUrl %}
        <img class="avatar" src="{{ resume.basicInfo.photoUrl }}" alt="个人照片" onerror="this.onerror=null; this.src='data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22150%22%20height%3D%22200%22%20viewBox%3D%220%200%20150%20200%22%3E%3Crect%20fill%3D%22%23f0f0f0%22%20width%3D%22150%22%20height%3D%22200%22%2F%3E%3Ctext%20fill%3D%22%23888%22%20font-family%3D%22sans-serif%22%20font-size%3D%2220%22%20dy%3D%22.3em%22%20text-anchor%3D%22middle%22%20x%3D%2275%22%20y%3D%22100%22%3E照片%3C%2Ftext%3E%3C%2Fsvg%3E'">
        {% endif %}
      </div>
    </section>
    {% endif %}

    <!-- 根据moduleOrders排序显示各个模块 -->
    {% for module in ordered_modules %}
      <!-- 求职意向 -->
      {% if module.key == 'jobIntention' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('求职意向') }}</div>
        </div>
        <div class="content job-intention-content">
          {% if module.data.position %}<div class="job-intention-item">期望职位：{{ module.data.position }}</div>{% endif %}
          {% if module.data.salary %}<div class="job-intention-item">期望薪资：{{ module.data.salary }}</div>{% endif %}
          {% if module.data.city %}<div class="job-intention-item">期望城市：{{ module.data.city }}</div>{% endif %}
          {% if module.data.status %}<div class="job-intention-item">求职状态：{{ module.data.status }}</div>{% endif %}
        </div>
      </section>
      {% endif %}

      <!-- 教育背景 -->
      {% if module.key == 'education' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('教育背景') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="education-header">
            <div class="school">{{ item.school }}</div>
            <div class="major-degree">{{ item.major }}{% if item.degree %}/{{ item.degree }}{% endif %}</div>
            <div class="edu-date">{{ item.startDate }} - {{ item.endDate }}</div>
          </div>
          <div class="edu-description">
            {% if item.description %}<span class="courses-label">主修课程：</span>{{ item.description }}{% endif %}
          </div>
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 实习经历 -->
      {% if module.key == 'internship' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('实习经历') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ item.company }}</div>
            <div class="company">{{ item.position }}</div>
            <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
          </div>
          {% if item.content %}
          <div class="description">{{ item.content | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 工作经历 -->
      {% if module.key == 'work' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('工作经历') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ item.company }}</div>
            <div class="company">{{ item.position }}</div>
            <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
          </div>
          {% if item.description %}
          <div class="description">{{ item.description | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 项目经历 -->
      {% if module.key == 'project' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('项目经历') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ item.projectName }}</div>
            <div class="company">{{ item.role }}</div>
            <div class="time">{{ item.startDate }} - {{ item.endDate }}</div>
          </div>
          {% if item.description %}
          <div class="description">{{ item.description | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 在校经历 -->
      {% if module.key == 'school' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('在校经历') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="school-experience-header">
            <div class="activity">{{ item.role }}</div>
            <div class="time">{{ item.startDate }}{% if item.endDate and item.endDate != item.startDate %} - {{ item.endDate }}{% endif %}</div>
          </div>
          {% if item.content %}
          <div class="description">{{ item.content | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 技能证书 -->
      {% if module.key == 'skills' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('技能证书') }}</div>
        </div>
        <div class="content">
          {% for skill in module.data %}
          <div class="skill">{{ skill }}</div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      <!-- 奖项荣誉 -->
      {% if module.key == 'awards' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('奖项荣誉') }}</div>
        </div>
        <div class="content">
          {% for award in module.data %}
          <div class="award">{{ award }}</div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      <!-- 兴趣爱好 -->
      {% if module.key == 'interests' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('兴趣爱好') }}</div>
        </div>
        <div class="content">
          {% for interest in module.data %}
          <div class="interest">{{ interest }}</div>
          {% endfor %}
        </div>
      </section>
      {% endif %}

      <!-- 自我评价 -->
      {% if module.key == 'evaluation' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('自我评价') }}</div>
        </div>
        <div class="content">
          <div class="description evaluation-content">
            {% if module.data is string %}
              {{ module.data }}
            {% elif module.data is sequence and module.data is not string %}
              {% if module.data[0] is string %}
                {% for eval in module.data %}
                  <p>{{ eval }}</p>
                {% endfor %}
              {% else %}
                {% for eval in module.data %}
                  <p>{{ eval.content if eval.content is defined else eval }}</p>
                {% endfor %}
              {% endif %}
            {% endif %}
          </div>
        </div>
      </section>
      {% endif %}

      <!-- 自定义模块1 -->
      {% if module.key == 'custom1' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('自定义模块1') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.customName }}</div>
            <div class="time">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</div>
          </div>
          {% if item.content %}
          <div class="description">{{ item.content | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 自定义模块2 -->
      {% if module.key == 'custom2' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('自定义模块2') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.customName }}</div>
            <div class="time">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</div>
          </div>
          {% if item.content %}
          <div class="description">{{ item.content | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}

      <!-- 自定义模块3 -->
      {% if module.key == 'custom3' and module.data %}
      <section class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ module.title | default('自定义模块3') }}</div>
        </div>
        {% for item in module.data %}
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.customName }}</div>
            <div class="time">{{ item.startDate }}{% if item.endDate %} - {{ item.endDate }}{% endif %}</div>
          </div>
          {% if item.content %}
          <div class="description">{{ item.content | safe }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </section>
      {% endif %}
    {% endfor %}
  </div>
</body>
</html>