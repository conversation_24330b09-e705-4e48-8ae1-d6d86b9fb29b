<!--
  templateA01.html
  说明：
  1. 本模板基于Jinja2语法，字段映射见app/schemas/resume.py。
  2. 样式和结构参考上传图片，SVG背景可后期替换。
  3. 各模块section可根据需要增删。
  4. 栏目顺序、显示与否、主色、行距均由渲染参数动态控制。
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>个人简历</title>
  <style>
    :root {
      --theme-color: {{ theme_color }};
      --spacing: {{ spacing }};
    }
    body {
      background: #f7f9fb;
      font-family: '微软雅黑', 'PingFang SC', Arial, sans-serif;
      color: #222;
      margin: 0;
      padding: 0;
    }
    .resume-container {
      max-width: 900px;
      margin: 40px auto 40px auto;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 4px 24px rgba(34, 68, 120, 0.08);
      padding: 40px 48px 32px 48px;
      position: relative;
      overflow: hidden;
    }
    .resume-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 32px;
    }
    .resume-title {
      font-size: 2.6rem;
      color: var(--theme-color);
      font-weight: bold;
      letter-spacing: 2px;
    }
    .resume-icons {
      display: flex;
      gap: 16px;
    }
    .resume-icons .icon {
      width: 38px;
      height: 38px;
      background: #eaf3fb;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--theme-color);
      font-size: 1.3rem;
      box-shadow: 0 2px 8px rgba(33, 118, 193, 0.08);
    }
    .section {
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1.5px solid #e0e0e0;
      position: relative;
      line-height: var(--spacing);
    }
    .section:last-child {
      border-bottom: none;
    }
    .section-title {
      font-size: 1.25rem;
      color: #fff;
      background: var(--theme-color);
      padding: 8px 28px;
      border-radius: 8px 8px 8px 0;
      font-weight: bold;
      margin-bottom: 18px;
      display: inline-block;
      position: relative;
      left: -24px;
      box-shadow: 2px 0 8px rgba(33, 118, 193, 0.04);
    }
    .basic-info-section {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 32px;
      padding-bottom: 24px;
      border-bottom: 1.5px solid #e0e0e0;
    }
    .basic-info-left {
      flex: 1 1 70%;
    }
    .basic-info-left table {
      width: 100%;
      border-collapse: collapse;
      font-size: 1.05rem;
    }
    .basic-info-left td {
      padding: 6px 8px;
      color: #333;
      vertical-align: middle;
    }
    .basic-info-photo {
      flex: 0 0 140px;
      display: flex;
      align-items: flex-start;
      justify-content: flex-end;
    }
    .basic-info-photo img {
      width: 120px;
      height: 150px;
      object-fit: cover;
      border-radius: 16px;
      border: 3px solid var(--theme-color);
      box-shadow: 0 2px 12px rgba(33, 118, 193, 0.10);
    }
    .education-item, .school-item, .internship-item, .work-item, .project-item, .custom-item {
      margin-bottom: 12px;
    }
    .education-time, .school-time, .internship-time, .work-time, .project-time, .custom-time {
      color: var(--theme-color);
      font-weight: bold;
      font-size: 1.05rem;
      margin-bottom: 2px;
      display: inline-block;
      min-width: 120px;
    }
    .education-main, .school-role, .internship-role, .work-role, .project-role, .custom-role {
      font-weight: bold;
      margin-right: 12px;
    }
    .education-desc, .school-content, .internship-content, .work-content, .project-content, .custom-content {
      color: #444;
      margin-left: 8px;
      font-size: 0.98rem;
    }
    .section-content ul, .section-content ol {
      margin: 0 0 0 18px;
      padding: 0;
    }
    .section-content li {
      margin-bottom: 6px;
      line-height: 1.7;
    }
    .section-content ol {
      list-style-type: decimal;
    }
    .section-content ul {
      list-style-type: disc;
    }
    @media (max-width: 700px) {
      .resume-container { padding: 12px; }
      .resume-header { flex-direction: column; align-items: flex-start; }
      .basic-info-section { flex-direction: column; }
      .basic-info-photo { margin-top: 18px; }
    }
  </style>
</head>
<body>
  <!-- SVG背景装饰（可替换） -->
  <svg class="resume-bg-svg" width="100%" height="120" viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg" style="position:absolute;top:0;left:0;z-index:-1;">
    <path d="M0,80 C480,160 960,0 1440,80 L1440,0 L0,0 Z" fill="#eaf3fb"/>
  </svg>
  <div class="resume-container">
    <!-- 顶部标题和icon -->
    <div class="resume-header">
      <div class="resume-title">个人简历</div>
      <div class="resume-icons">
        <!-- 四个icon -->
      </div>
    </div>
    <!-- 基本信息区 -->
    <div class="section basic-info-section">
      <div class="basic-info-left">
        <table>
          <tr>
            <td>姓&nbsp;&nbsp;&nbsp;&nbsp;名：</td>
            <td>{{ resume.basicInfo.name }}</td>
            <td>求职意向：</td>
            <td>{{ resume.jobIntention.position }}</td>
          </tr>
          <tr>
            <td>民&nbsp;&nbsp;&nbsp;&nbsp;族：</td>
            <td>{{ resume.basicInfo.nation }}</td>
            <td>出生年月：</td>
            <td>{{ resume.basicInfo.birthday }}</td>
          </tr>
          <tr>
            <td>籍&nbsp;&nbsp;&nbsp;&nbsp;贯：</td>
            <td>{{ resume.basicInfo.hometown }}</td>
            <td>身&nbsp;&nbsp;&nbsp;&nbsp;高：</td>
            <td>{{ resume.basicInfo.height }}</td>
          </tr>
          <tr>
            <td>电&nbsp;&nbsp;&nbsp;&nbsp;话：</td>
            <td>{{ resume.basicInfo.phone }}</td>
            <td>学&nbsp;&nbsp;&nbsp;&nbsp;历：</td>
            <td>{{ resume.basicInfo.educationLevel }}</td>
          </tr>
          <tr>
            <td>邮&nbsp;&nbsp;&nbsp;&nbsp;箱：</td>
            <td>{{ resume.basicInfo.email }}</td>
            <td>政治面貌：</td>
            <td>{{ resume.basicInfo.politics }}</td>
          </tr>
        </table>
      </div>
      <div class="basic-info-photo">
        {% if resume.basicInfo.photoUrl %}
          <img src="data:image/jpeg;base64,{{ resume.basicInfo.photoUrl }}" alt="个人照片" />
        {% endif %}
      </div>
    </div>
    <!-- 动态渲染各栏目，顺序由ordered_modules决定 -->
    {% for module in ordered_modules %}
      {% if module.data and module.data|length > 0 %}
        <div class="section">
          <div class="section-title">{{ module.title }}</div>
          <div class="section-content">
            {% if module.key == 'education' %}
              {% for item in module.data %}
                <div class="education-item">
                  <div class="education-time">{{ item.startDate }} - {{ item.endDate }}</div>
                  <div class="education-main">
                    <span class="education-school">{{ item.school }}</span>
                    <span class="education-major">{{ item.major }}</span>
                    <span class="education-degree">{{ item.degree }}</span>
                  </div>
                  <div class="education-desc">{{ item.description }}</div>
                </div>
              {% endfor %}
            {% elif module.key == 'school' %}
              <ul>
              {% for item in module.data %}
                <li>
                  <span class="school-time">{{ item.startDate }} - {{ item.endDate }}</span>
                  <span class="school-role">{{ item.role }}</span>
                  <span class="school-content">{{ item.content }}</span>
                </li>
              {% endfor %}
              </ul>
            {% elif module.key == 'internship' %}
              {% for item in module.data %}
                <div class="internship-item">
                  <div class="internship-time">{{ item.startDate }} - {{ item.endDate }}</div>
                  <span class="internship-role">{{ item.position }}</span>
                  <span class="internship-company">{{ item.company }}</span>
                  <div class="internship-content">{{ item.content }}</div>
                </div>
              {% endfor %}
            {% elif module.key == 'work' %}
              {% for item in module.data %}
                <div class="work-item">
                  <div class="work-time">{{ item.startDate }} - {{ item.endDate }}</div>
                  <span class="work-role">{{ item.position }}</span>
                  <span class="work-company">{{ item.company }}</span>
                  <div class="work-content">{{ item.description }}</div>
                </div>
              {% endfor %}
            {% elif module.key == 'project' %}
              {% for item in module.data %}
                <div class="project-item">
                  <div class="project-time">{{ item.startDate }} - {{ item.endDate }}</div>
                  <span class="project-role">{{ item.role }}</span>
                  <span class="project-name">{{ item.projectName }}</span>
                  <div class="project-content">{{ item.description }}</div>
                </div>
              {% endfor %}
            {% elif module.key == 'skills' %}
              <ul>
              {% for skill in module.data %}
                <li>{{ skill }}</li>
              {% endfor %}
              </ul>
            {% elif module.key == 'awards' %}
              <ul>
              {% for award in module.data %}
                <li>{{ award }}</li>
              {% endfor %}
              </ul>
            {% elif module.key == 'interests' %}
              <ul>
              {% for interest in module.data %}
                <li>{{ interest }}</li>
              {% endfor %}
              </ul>
            {% elif module.key == 'evaluation' %}
              <ol>
              {% for eva in module.data %}
                <li>{{ eva.content }}</li>
              {% endfor %}
              </ol>
            {% elif module.key in ['custom1', 'custom2', 'custom3'] %}
              {% for item in module.data %}
                <div class="custom-item">
                  <div class="custom-time">{{ item.startDate }} - {{ item.endDate }}</div>
                  <span class="custom-role">{{ item.role }}</span>
                  <span class="custom-name">{{ item.customName }}</span>
                  <div class="custom-content">{{ item.content }}</div>
                </div>
              {% endfor %}
            {% endif %}
          </div>
        </div>
      {% endif %}
    {% endfor %}
  </div>
</body>
</html>
