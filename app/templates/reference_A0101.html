<!-- 由微信wxml+exss转换为标准HTML+CSS，结构和样式尽量保持一致 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <title>参考A01模板</title>
  <style>
/* 以下为原exss样式，单位rpx可替换为px或rem，变量用:root声明 */
:root {
  --theme-color: #2B6CB0;
  --font-size: 12px;
  --spacing: 1;
  --text-color: #333333;
  --secondary-color: #666666;
}

.resumeTemplateA01 {
  width: 100%;
  aspect-ratio: 1 / 1.4142;
  max-width: 794px;
  margin: 0 auto;
  padding: 20px 40px 40px 40px;
  font-size: var(--font-size);
  line-height: calc(var(--spacing) * 1.3);
  color: var(--text-color);
  box-sizing: border-box;
  background: #FFFFFF;
  position: relative;
}
.resumeTemplateA01::before {
  content: '';
  position: absolute;
  left: 60px;
  top: 100px;
  bottom: 40px;
  width: 1px;
  background-color: var(--theme-color);
  transform: scaleX(0.3);
  transform-origin: left;
}
/* ... 省略其余样式，直接复制exss内容并将rpx替换为px ... */
</style>
</head>
<body>
<div class="resumeTemplateA01">
  <!-- 主标题 -->
  <div class="resume-header">
    <div class="resume-title">个人简历</div>
    <div class="icon-group">
      <img class="icon-circle" src="../images/icons/pencil.svg" />
      <img class="icon-circle" src="../images/icons/graduation-cap.svg" />
      <img class="icon-circle" src="../images/icons/briefcase.svg" />
    </div>
  </div>

  <!-- 提醒图标 -->
  <div class="tixing-wrapper">
    <div class="tixing-container1">
      <div class="tixing-icon"></div>
    </div>
    <div class="tixing-container2">
      <img class="tixing2-image" src="./images/tiXing2.svg" style="object-fit:contain;" />
    </div>
  </div>

  <!-- 使用sortedModules来控制模块显示顺序 -->
  {% for item in sortedModules %}
    <!-- 基本信息模块 -->
    {% if item.type == 'basicInfo' and item.data %}
      <div class="section">
        {% if item.data.title %}
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ item.data.title }}</div>
        </div>
        {% endif %}
        <div class="basic-info">
          <div class="info-content">
            <div class="info-grid">
              {% if item.data.name %}<span class="info-item">姓　　名：{{ item.data.name }}</span>{% endif %}
              {% if item.data.birthday %}<span class="info-item">生　　日：{{ item.data.birthday }}</span>{% endif %}
              {% if item.data.phone %}<span class="info-item">电　　话：{{ item.data.phone }}</span>{% endif %}
              {% if item.data.marriage %}<span class="info-item">婚　　姻：{{ item.data.marriage }}</span>{% endif %}
              {% if item.data.email %}<span class="info-item">邮　　箱：{{ item.data.email }}</span>{% endif %}
              {% if item.data.politics %}<span class="info-item">政治面貌：{{ item.data.politics }}</span>{% endif %}
              {% if item.data.city %}<span class="info-item">城　　市：{{ item.data.city }}</span>{% endif %}
              {% if item.data.nation %}<span class="info-item">民　　族：{{ item.data.nation }}</span>{% endif %}
              {% if item.data.age %}<span class="info-item">年　　龄：{{ item.data.age }}岁</span>{% endif %}
              {% if item.data.hometown %}<span class="info-item">籍　　贯：{{ item.data.hometown }}</span>{% endif %}
              {% if item.data.gender %}<span class="info-item">性　　别：{{ item.data.gender }}</span>{% endif %}
              {% if item.data.height %}<span class="info-item">身　　高：{{ item.data.height }}cm</span>{% endif %}
              {% if item.data.wechat %}<span class="info-item">微　　信：{{ item.data.wechat }}</span>{% endif %}
              {% if item.data.weight %}<span class="info-item">体　　重：{{ item.data.weight }}kg</span>{% endif %}
              {% if item.data.customContent1 %}<span class="info-item">{{ item.data.customTitle1 or '自定义标题1' }}：{{ item.data.customContent1 }}</span>{% endif %}
              {% if item.data.customContent2 %}<span class="info-item">{{ item.data.customTitle2 or '自定义标题2' }}：{{ item.data.customContent2 }}</span>{% endif %}
            </div>
          </div>
          {% if item.data.photoUrl %}
          <img class="avatar" src="{{ item.data.photoUrl }}" style="object-fit:cover;" />
          {% endif %}
        </div>
      </div>
    {% endif %}

    <!-- 求职意向模块 -->
    {% if item.type == 'jobIntention' and item.data %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">求职意向</div>
        </div>
        <div class="content job-intention-content">
          {% if item.data.position %}<div class="job-intention-item">期望职位：{{ item.data.position }}</div>{% endif %}
          {% if item.data.salary %}<div class="job-intention-item">期望薪资：{{ item.data.salary }}</div>{% endif %}
          {% if item.data.city %}<div class="job-intention-item">期望城市：{{ item.data.city }}</div>{% endif %}
          {% if item.data.status %}<div class="job-intention-item">求职状态：{{ item.data.status }}</div>{% endif %}
        </div>
      </div>
    {% endif %}

    <!-- 在校经历模块 -->
    {% if item.type == 'school' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">在校经历</div>
        </div>
        {% for schoolItem in item.data %}
        <div class="content">
          <div class="school-experience-header">
            <div class="activity">{{ schoolItem.role }}</div>
            <div class="time">{{ schoolItem.startDate }} - {{ schoolItem.endDate }}</div>
          </div>
          {% if schoolItem.content %}
          <div class="description">{{ schoolItem.content }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- 教育经历模块 -->
    {% if item.type == 'education' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">教育经历</div>
        </div>
        {% for eduItem in item.data %}
        <div class="content">
          <div class="education-header">
            <div class="school">{{ eduItem.school }}</div>
            <div class="major-degree">{{ eduItem.major }}{% if eduItem.degree %}/{{ eduItem.degree }}{% endif %}</div>
            <div class="edu-date">{{ eduItem.startDate }} - {{ eduItem.endDate }}</div>
          </div>
          <div class="edu-description">
            {% if eduItem.courses %}<span class="courses-label">主修课程：</span>{{ eduItem.courses }}<br>{% endif %}
            {% if eduItem.description %}{{ eduItem.description }}{% endif %}
          </div>
        </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- 实习经历模块 -->
    {% if item.type == 'internship' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">实习经历</div>
        </div>
        {% for internItem in item.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ internItem.company }}</div>
            {% if internItem.position %}<div class="company">{{ internItem.position }}</div>{% endif %}
            <div class="time">{{ internItem.startDate }} - {{ internItem.endDate }}</div>
          </div>
          {% if internItem.content %}
          <div class="description">{{ internItem.content }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- 工作经历模块 -->
    {% if item.type == 'work' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">工作经历</div>
        </div>
        {% for workItem in item.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ workItem.company }}</div>
            {% if workItem.position %}<div class="company">{{ workItem.position }}</div>{% endif %}
            <div class="time">{{ workItem.startDate }} - {{ workItem.endDate }}</div>
          </div>
          {% if workItem.content %}
          <div class="description">{{ workItem.content }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- 项目经历模块 -->
    {% if item.type == 'project' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">项目经历</div>
        </div>
        {% for projectItem in item.data %}
        <div class="content">
          <div class="internship-header">
            <div class="company">{{ projectItem.projectName }}</div>
            {% if projectItem.role %}<div class="company">{{ projectItem.role }}</div>{% endif %}
            <div class="time">{{ projectItem.startDate }} - {{ projectItem.endDate }}</div>
          </div>
          {% if projectItem.description %}
          <div class="description">{{ projectItem.description }}</div>
          {% endif %}
        </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- 技能特长模块 -->
    {% if item.type == 'skills' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">技能特长</div>
        </div>
        <div class="content">
          {% for skillItem in item.data %}
          <span class="skill">{{ skillItem }}</span>
          {% endfor %}
        </div>
      </div>
    {% endif %}

    <!-- 获奖证书模块 -->
    {% if item.type == 'awards' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">获奖证书</div>
        </div>
        <div class="content">
          {% for awardItem in item.data %}
          <span class="award">{{ awardItem }}</span>
          {% endfor %}
        </div>
      </div>
    {% endif %}

    <!-- 兴趣爱好模块 -->
    {% if item.type == 'interests' and item.data|length > 0 %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">兴趣爱好</div>
        </div>
        <div class="content">
          {% for interestItem in item.data %}
          <span class="interest">{{ interestItem }}</span>
          {% endfor %}
        </div>
      </div>
    {% endif %}

    <!-- 自我评价模块 -->
    {% if item.type == 'evaluation' and item.data and item.data[0].content %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">自我评价</div>
        </div>
        <div class="content">
          <div class="description">{{ item.data[0].content }}</div>
        </div>
      </div>
    {% endif %}

    <!-- 自定义模块1 -->
    {% if item.type == 'custom1' and item.data %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ item.data.title }}</div>
        </div>
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.data.role }}</div>
            <div class="time">{{ item.data.startDate }} - {{ item.data.endDate }}</div>
          </div>
          <div class="description">{{ item.data.content }}</div>
        </div>
      </div>
    {% endif %}
    <!-- 自定义模块2 -->
    {% if item.type == 'custom2' and item.data %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ item.data.title }}</div>
        </div>
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.data.role }}</div>
            <div class="time">{{ item.data.startDate }} - {{ item.data.endDate }}</div>
          </div>
          <div class="description">{{ item.data.content }}</div>
        </div>
      </div>
    {% endif %}
    <!-- 自定义模块3 -->
    {% if item.type == 'custom3' and item.data %}
      <div class="section">
        <div class="title-wrapper">
          <div class="title-bg"></div>
          <div class="title">{{ item.data.title }}</div>
        </div>
        <div class="content">
          <div class="custom-header">
            <div class="custom-name">{{ item.data.role }}</div>
            <div class="time">{{ item.data.startDate }} - {{ item.data.endDate }}</div>
          </div>
          <div class="description">{{ item.data.content }}</div>
        </div>
      </div>
    {% endif %}
  {% endfor %}
</div>
</body>
</html> 