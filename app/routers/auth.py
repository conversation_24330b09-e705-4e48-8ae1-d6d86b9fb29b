"""
用户认证相关API路由
"""
from fastapi import APIRouter, HTTPException, Depends, status
from sqlalchemy.orm import Session
from datetime import timedelta
import logging

from app.database import get_db
from app.models import User
from app.schemas.user import (
    WeChatLoginRequest, 
    WeChatLoginResponse, 
    UserInfo,
    UserUpdate,
    MessageResponse
)
from app.services.wechat_service import wechat_service
from app.auth import create_access_token, get_current_user
from app.config import settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/auth",
    tags=["认证"],
    responses={404: {"description": "未找到"}},
)

@router.post("/login", response_model=WeChatLoginResponse)
async def wechat_login(
    login_data: WeChatLoginRequest,
    db: Session = Depends(get_db)
):
    """
    微信小程序登录
    """
    try:
        # 通过微信API获取用户session信息
        session_data = await wechat_service.code_to_session(login_data.code)
        if not session_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="微信登录失败，请检查code是否有效"
            )
        
        openid = session_data.get("openid")
        unionid = session_data.get("unionid")
        
        # 查找或创建用户
        user = db.query(User).filter(User.openid == openid).first()
        
        if not user:
            # 创建新用户
            user_data = {
                "openid": openid,
                "unionid": unionid,
            }
            
            # 如果提供了用户信息，则更新用户数据
            if login_data.user_info:
                user_info = login_data.user_info
                if wechat_service.validate_user_info(user_info):
                    user_data.update({
                        "nickname": user_info.get("nickName"),
                        "avatar_url": user_info.get("avatarUrl"),
                        "gender": user_info.get("gender", 0),
                        "country": user_info.get("country"),
                        "province": user_info.get("province"),
                        "city": user_info.get("city"),
                    })
            
            user = User(**user_data)
            db.add(user)
            db.commit()
            db.refresh(user)
            logger.info(f"创建新用户: {user.id}")
        
        else:
            # 更新现有用户信息
            if login_data.user_info:
                user_info = login_data.user_info
                if wechat_service.validate_user_info(user_info):
                    user.nickname = user_info.get("nickName", user.nickname)
                    user.avatar_url = user_info.get("avatarUrl", user.avatar_url)
                    user.gender = user_info.get("gender", user.gender)
                    user.country = user_info.get("country", user.country)
                    user.province = user_info.get("province", user.province)
                    user.city = user_info.get("city", user.city)
                    
                    if unionid and not user.unionid:
                        user.unionid = unionid
                    
                    db.commit()
                    db.refresh(user)
                    logger.info(f"更新用户信息: {user.id}")
        
        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(user.id)}, 
            expires_delta=access_token_expires
        )
        
        return WeChatLoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user_info=UserInfo.model_validate(user)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception("微信登录处理异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登录处理失败"
        )

@router.get("/user", response_model=UserInfo)
async def get_current_user_info(
    current_user: User = Depends(get_current_user)
):
    """
    获取当前用户信息
    """
    return UserInfo.model_validate(current_user)

@router.put("/user", response_model=UserInfo)
async def update_user_info(
    user_update: UserUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    更新用户信息
    """
    try:
        # 更新用户信息
        update_data = user_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(current_user, field, value)
        
        db.commit()
        db.refresh(current_user)
        
        logger.info(f"用户 {current_user.id} 更新信息成功")
        return UserInfo.model_validate(current_user)
        
    except Exception as e:
        logger.exception("更新用户信息异常")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新用户信息失败"
        )

@router.post("/refresh", response_model=WeChatLoginResponse)
async def refresh_token(
    current_user: User = Depends(get_current_user)
):
    """
    刷新访问令牌
    """
    try:
        # 创建新的访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        access_token = create_access_token(
            data={"sub": str(current_user.id)}, 
            expires_delta=access_token_expires
        )
        
        return WeChatLoginResponse(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user_info=UserInfo.model_validate(current_user)
        )
        
    except Exception as e:
        logger.exception("刷新令牌异常")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="刷新令牌失败"
        )
