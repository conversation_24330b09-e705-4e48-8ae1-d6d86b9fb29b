# 微信小程序开发文档

## 1. 项目概述
本项目是一个简历制作小程序，提供用户访问记录、会员管理等功能。

## 2. 技术栈
- 前端：微信小程序
- 后端：Python Flask
- 数据库：MySQL

## 3. 数据库设计
### 3.1 用户表(users)
- id: 主键
- openid: 微信openid
- unionid: 微信unionid
- nickname: 昵称
- avatar_url: 头像
- gender: 性别
- country: 国家
- province: 省份
- city: 城市
- created_at: 创建时间
- updated_at: 更新时间

### 3.2 会员表(members)
- id: 主键
- user_id: 用户ID
- level: 会员等级
- points: 积分
- vip_expire_time: 会员过期时间
- created_at: 创建时间
- updated_at: 更新时间

### 3.3 访问记录表(visit_logs)
- id: 主键
- user_id: 用户ID
- page_path: 访问页面
- visit_time: 访问时间
- stay_duration: 停留时长
- device_info: 设备信息
- ip_address: IP地址

### 3.4 用户行为表(user_actions)
- id: 主键
- user_id: 用户ID
- action_type: 行为类型
- action_content: 行为内容
- action_time: 行为时间

## 4. API接口
### 4.1 用户相关
#### 登录/注册
- 请求方式：POST
- 接口地址：/api/user/login
- 请求参数：
  - code: 微信登录code
- 返回数据：
  - token: 用户token
  - userInfo: 用户信息

#### 获取用户信息
- 请求方式：GET
- 接口地址：/api/user/info
- 请求头：
  - Authorization: Bearer {token}
- 返回数据：
  - userInfo: 用户信息

### 4.2 会员相关
#### 获取会员信息
- 请求方式：GET
- 接口地址：/api/member/info
- 请求头：
  - Authorization: Bearer {token}
- 返回数据：
  - memberInfo: 会员信息

### 4.3 访问记录相关
#### 记录访问
- 请求方式：POST
- 接口地址：/api/visit/log
- 请求头：
  - Authorization: Bearer {token}
- 请求参数：
  - pagePath: 页面路径
  - stayDuration: 停留时长

### 4.4 用户行为相关
#### 记录行为
- 请求方式：POST
- 接口地址：/api/action/log
- 请求头：
  - Authorization: Bearer {token}
- 请求参数：
  - actionType: 行为类型
  - content: 行为内容

## 5. 前端开发指南
### 5.1 初始化配置
1. 在app.js中配置请求拦截器
2. 在app.js中配置登录逻辑
3. 在app.js中配置访问记录

### 5.2 页面开发
1. 在页面onLoad时记录访问
2. 在页面onShow时记录停留时长
3. 在用户操作时记录行为

### 5.3 工具函数
1. 登录函数
2. 访问记录函数
3. 行为记录函数

## 6. 部署说明
### 6.1 环境要求
- Python 3.8+
- MySQL 5.7+
- 微信开发者工具

### 6.2 部署步骤
1. 安装依赖
2. 配置数据库
3. 配置微信小程序
4. 启动服务

## 7. 注意事项
1. 用户隐私保护
2. 数据安全存储
3. 接口访问限制
4. 错误处理机制
